/**
 * @file transactionCreate_JournalManagement_Module.js
 * @description 處理會計分錄模組
 */

class Journal {
    //會計科目常數定義
    static CASH ={code: '1105', name: '現金'};//
    static BANK_DEPOSIT = {code: '1110', name: '銀行存款'};
    static DEPOSIT_GUARANTEE = {code: '1910', name: '存出保證金'};//存出保證金屬於資產
    static FEE = {code: '6270', name: '手續費'};//屬於營業費用
    static PAYABLE_TAX = {code: '1290', name: '進項稅額'};//支付款項時表示有進項貨物或服務，可用於抵扣銷項稅額屬於資產
    static SALE_TAX = {code: '2290', name: '銷項稅額'};//收取款項時表示有銷出貨物或服務，待繳納營業稅屬於負債
    static PAYABLE_ACCOUNT = {code: '2145', name: '應付帳款'};//屬於負債
    static ADVANCE_PAYMENT_ACCOUNT = {code: '2165', name: '暫預收帳款'};//屬於負債
    static RECEIVABLE_ACCOUNT = {code: 1130, name: '應收帳款'};//屬於資產
    static PREPAYMENT_ACCOUNT = {code: 1210, name: '暫預付帳款'};//屬於資產
    static OPERATING_FUND = {code: 3310, name: '資本額股本'};//屬於權益基金
    static SHAREHOLDERS = {code: 2210, name: '股東往來'};//屬於負債
    //帳款到帳情形
    static SAME_DAY = {key: 'same_day', name: '同日'};
    static RECEIVABLE = {key: 'receivable', name: '應收'};
    static PREPAYMENT = {key: 'prepayment', name: '預付'};
    static DIFFERENT_DAY = {key: 'different_day', name: '非同日'};
    //帳戶類型種類
    static AccountType_OPERATING_FUND = {key: 'operating_fund', name: '營運資金帳戶'};
    static AccountType_SHAREHOLDERS = {key: 'shareholders', name: '股東往來帳戶'};
    static AccountType_BANK_DEPOSIT = {key: 'bank_deposit', name: '銀行存款帳戶'};
    static AccountType_ENGINEERING_GUARANTEE = {key: 'engineering Guarantee', name: '工程保證金帳戶'};
    static AccountType_CASH = {key: 'cash', name: '現金'};

    //start--需求輸入
    #type // 'expense' or 'income'
    #status // 'same_day' or 'receivable' or 'prepayment'
    
    #mainAccountName // 主交易帳戶名稱
    #mainAccountType // 主交易帳戶類型
    #counterpartyName // 交易對象名稱
    #counterpartyType // 交易對象類型

    #mainAmount // 主交易金額
    #taxAmount // 稅額
    #feeAmount // 手續費
    #totalAmount // 總金額
    
    #paymentDate // 付款日期
    #invoiceDate // 開立憑證日期

    #description // 交易項目描述
    #descriptionCode // 交易項目代碼
    //end--需求輸入

    //start--會計分錄
    #debit // 借方(收入方)陣列
    #credit // 貸方(支出方)陣列

    #journal // 會計分錄結果物件
    //end--會計分錄


    constructor(formData) {
        this.#debit = [];
        this.#credit = [];
        this.#journal = {};
        
        this.#type = formData.type;
        this.#status = formData.status;
        this.#mainAccountName = formData.mainAccountName;
        this.#mainAccountType = formData.mainAccountType;
        this.#counterpartyName = formData.counterpartyName;
        this.#mainAmount = formData.mainAmount;
        this.#taxAmount = formData.taxAmount;
        this.#feeAmount = formData.feeAmount;
        this.#totalAmount = formData.totalAmount;
        this.#paymentDate = formData.paymentDate;
        this.#invoiceDate = formData.invoiceDate;
        this.#description = formData.description;
        this.#descriptionCode = formData.descriptionCode;
    }

    //進行會計分錄--
    /**
     * 第一步進行 設定借貸雙方
     * 
     * 第二步進行 設定借貸雙方之會計科目
     * 
     * 如果借方(收入方)的屬於帳戶類型請參考下方：
     * [營運資金帳戶]，則借方之會計科目為[負債/流動負債/股東權益]
     * [現金]，則借方之會計科目為[資產/流動資產/現金]
     * [銀行存款帳戶]，則借方之會計科目為[資產/流動資產/銀行存款]
     * [工程保證金帳戶]，則借方之會計科目為[資產/流動或非流動資產/存出保證金]
     * [其他帳戶]，則借方之會計科目為則依照交易項目決定。
     * 
     *      
     * 第三步進行 設定通用之會計科目(稅別、應收付帳款情形、手續費用、)
     * 
     * 如果有手續費，則將手續費放入[資金帳戶]本身，看是借方還是貸方
     * 如果有稅別，則將稅別放入[資金帳戶]本身，看是借方還是貸方
     * 如果有應收付帳款情形，則將應收付帳款情形放入[資金帳戶]本身，看是借方還是貸方
     */
    #processJournal() {
        let debitEntityName = null;
        let creditEntityName = null;
        if (this.#type === 'expense') {
            //如果為支出時，則借方(收入方)為[交易對象]，貸方(支出方)為[我方主要帳戶]
            //取得[交易對象]名稱，放入借方(收入方)帳戶對象
            debitEntityName = this.#counterpartyName;
            //取得[我方主要帳戶]名稱，放入貸方(支出方)帳戶對象
            creditEntityName = this.#mainAccountName;

            //----處理借方(收入方)科目-----
            //處理手續費 (永遠是借方費用)，如果為應收付帳款(receivable)時，表示無現金交易，應無手續費產生。
            if (this.#feeAmount > 0 && this.#status !== 'receivable') {
                this.#debit.push({ date: this.#paymentDate, code: Journal.FEE.code, name: Journal.FEE.name, amount: this.#feeAmount });
            }

            //處理主要交易與稅額
            this.#debit.push({ date: this.#paymentDate, code: this.#descriptionCode, name: this.#description, amount: this.#totalAmount });
            if (this.#taxAmount > 0) {
                //處理稅額，為支出時，屬於進項
                this.#debit.push({ date: this.#paymentDate, code: Journal.PAYABLE_TAX.code, name: Journal.PAYABLE_TAX.name, amount: this.#taxAmount });
            }

            //----處理貸方(支出方)科目-----
            //判斷科目屬於銀行存款、現金、存出保證金
            switch (this.#status) {
                case Journal.SAME_DAY.key: //同日(現金)
                    if(this.#mainAccountType === Journal.AccountType_CASH.name) {//現金
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.CASH.code, name: Journal.CASH.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_BANK_DEPOSIT.name) {//銀行存款
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.BANK_DEPOSIT.code, name: Journal.BANK_DEPOSIT.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_ENGINEERING_GUARANTEE.name) {//存出保證金
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.DEPOSIT_GUARANTEE.code, name: Journal.DEPOSIT_GUARANTEE.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_OPERATING_FUND.name) {//營運資金帳戶
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.OPERATING_FUND.code, name: Journal.OPERATING_FUND.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_SHAREHOLDERS.name) {//股東往來
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.SHAREHOLDERS.code, name: Journal.SHAREHOLDERS.name, amount: this.#mainAmount + this.#feeAmount });
                    }
                    break;
                case Journal.RECEIVABLE.key: //應付(權責)
                    //應付帳款PAYABLE_ACCOUNT
                    this.#credit.push({ date: this.#invoiceDate, code: Journal.PAYABLE_ACCOUNT.code, name: Journal.PAYABLE_ACCOUNT.name , amount: this.#mainAmount });
                    break;
                case Journal.PREPAYMENT.key: //預付(權責)
                    // 當預付時，借方主要科目應為「預付費用」而非費用本身，故先處理上方已存在的費用
                    //this.#debit.push({ date: this.#paymentDate, code: this.#descriptionCode, name: this.#description, amount: this.#totalAmount });
                    this.#debit = this.#debit.filter(item => item.name !== this.#description && item.name !== Journal.PAYABLE_TAX.name); // 移除原費用及稅金
                    this.#debit.unshift({ date: this.#paymentDate, code: Journal.PREPAYMENT_ACCOUNT.code, name: Journal.PREPAYMENT_ACCOUNT.name, amount: this.#mainAmount });
                    //暫時預付帳款，表示需從銀行或現金部分支付
                    if(this.#mainAccountType === Journal.AccountType_CASH.name) {//現金
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.CASH.code, name: Journal.CASH.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_BANK_DEPOSIT.name) {//銀行存款
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.BANK_DEPOSIT.code, name: Journal.BANK_DEPOSIT.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_ENGINEERING_GUARANTEE.name) {//存出保證金
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.DEPOSIT_GUARANTEE.code, name: Journal.DEPOSIT_GUARANTEE.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_OPERATING_FUND.name) {//營運資金帳戶
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.OPERATING_FUND.code, name: Journal.OPERATING_FUND.name, amount: this.#mainAmount + this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_SHAREHOLDERS.name) {//股東往來
                        this.#credit.push({ date: this.#invoiceDate, code: Journal.SHAREHOLDERS.code, name: Journal.SHAREHOLDERS.name, amount: this.#mainAmount + this.#feeAmount });
                    }
                    break;
                case Journal.DIFFERENT_DAY.key: //非同日(現金)
                    this.#credit.push({ date: this.#invoiceDate, code: 'xxxx', name: '尚未處理非同日', amount: this.#mainAmount });
                    break;
                default:
                    break;
            }
        }else if (this.#type === 'income') {
            //如果為收入，則借方(收入方)為[我方主要帳戶]，貸方(支出方)為[交易對象]
            //取得[我方主要帳戶]名稱，放入借方(收入方)帳戶對象
            debitEntityName = this.#mainAccountName;
            //取得[交易對象]名稱，放入貸方(支出方)帳戶對象
            creditEntityName = this.#counterpartyName;
            

            //----處理貸方(支出方)科目-----
            
            //處理主要交易與稅額
            this.#credit.push({ date: this.#paymentDate, code: this.#descriptionCode, name: this.#description, amount: this.#totalAmount });
            if (this.#taxAmount > 0) {
                //處理稅額，為收入時，屬於銷項稅額
                this.#credit.push({ date: this.#paymentDate, code: Journal.SALE_TAX.code, name: Journal.SALE_TAX.name, amount: this.#taxAmount });
            }


            //----處理借方(收入方)科目-----
            //處理手續費 (永遠是借方費用)，如果為應收付帳款(receivable)時，表示無現金交易，應無手續費產生。
            if (this.#feeAmount > 0 && this.#status !== 'receivable') {
                this.#debit.push({ date: this.#paymentDate, code: Journal.FEE.code, name: Journal.FEE.name, amount: this.#feeAmount });
            }

            //判斷科目屬於銀行存款、現金、存出保證金
            switch (this.#status) {
                case Journal.SAME_DAY.key: //同日(現金)
                    if(this.#mainAccountType === Journal.AccountType_CASH.name) {//現金
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.CASH.code, name: Journal.CASH.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_BANK_DEPOSIT.name) {//銀行存款
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.BANK_DEPOSIT.code, name: Journal.BANK_DEPOSIT.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_ENGINEERING_GUARANTEE.name) {//存出保證金
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.DEPOSIT_GUARANTEE.code, name: Journal.DEPOSIT_GUARANTEE.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_OPERATING_FUND.name) {//營運資金帳戶
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.OPERATING_FUND.code, name: Journal.OPERATING_FUND.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_SHAREHOLDERS.name) {//股東往來
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.SHAREHOLDERS.code, name: Journal.SHAREHOLDERS.name, amount: this.#mainAmount - this.#feeAmount });
                    }
                    break;
                case Journal.RECEIVABLE.key: //應收(權責)
                    //應收帳款RECEIVABLE_ACCOUNT
                    this.#debit.push({ date: this.#invoiceDate, code: Journal.RECEIVABLE_ACCOUNT.code, name: Journal.RECEIVABLE_ACCOUNT.name , amount: this.#mainAmount });
                    break;
                case Journal.PREPAYMENT.key: //預收(權責)
                    // 當預收時，借方主要科目應為「預收費用」而非費用本身，故先處理上方已存在的費用
                    this.#credit = this.#credit.filter(item => item.name !== this.#description && item.name !== Journal.SALE_TAX.name); // 移除原費用及稅金
                    this.#credit.unshift({ date: this.#paymentDate, code: Journal.ADVANCE_PAYMENT_ACCOUNT.code, name: Journal.ADVANCE_PAYMENT_ACCOUNT.name, amount: this.#mainAmount });
                    //暫時預收帳款，表示需從銀行或現金部分支付
                    if(this.#mainAccountType === Journal.AccountType_CASH.name) {//現金
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.CASH.code, name: Journal.CASH.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_BANK_DEPOSIT.name) {//銀行存款
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.BANK_DEPOSIT.code, name: Journal.BANK_DEPOSIT.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_ENGINEERING_GUARANTEE.name) {//存出保證金
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.DEPOSIT_GUARANTEE.code, name: Journal.DEPOSIT_GUARANTEE.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_OPERATING_FUND.name) {//營運資金帳戶
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.OPERATING_FUND.code, name: Journal.OPERATING_FUND.name, amount: this.#mainAmount - this.#feeAmount });
                    }else if(this.#mainAccountType === Journal.AccountType_SHAREHOLDERS.name) {//股東往來
                        this.#debit.push({ date: this.#invoiceDate, code: Journal.SHAREHOLDERS.code, name: Journal.SHAREHOLDERS.name, amount: this.#mainAmount - this.#feeAmount });
                    }
                    break;
                case Journal.DIFFERENT_DAY.key: //非同日(現金)
                    this.#debit.push({ date: this.#invoiceDate, code: 'xxxx', name: '尚未處理非同日', amount: this.#mainAmount });
                    break;
                default:
                    break;
            }


            

        }
        const journal = {
            debitEntity: debitEntityName,
            creditEntity: creditEntityName,
            debit: this.#debit,
            credit: this.#credit
        };
        this.#journal = journal;
    }
    //end--進行會計分錄

    getJournal() {
        this.#processJournal();
        return this.#journal;
    }
}

//-------------- UI 處理常式 --------------



// --- 核心會計邏輯與畫面更新函式 ---
const updatePreview = async (e) => {
    //console.log('updatePreview', e);
    const { feeToggle } = getTransactionFormCard_DOMElements();

    // 1. 取得所有輸入值
    const formData = getTransactionFormData();
    const feeCheck = feeToggle.checked;

    // 2. 初始化分錄物件
    const journalFormData = await transformFormDataToJournalData(feeCheck,formData)

    const mjournal = new Journal(journalFormData);

    // 3. 判斷會計分錄邏輯
    const journalList = mjournal.getJournal();

    // 4. 渲染畫面
    renderJournal(journalList);
};

// --- 畫面渲染輔助函式 ---
const renderJournal = (journal) => {
    const { creditContainer, debitContainer, creditEntityEl, debitEntityEl } = getJournalCard_DOMElements();
    creditEntityEl.textContent = journal.creditEntity;
    debitEntityEl.textContent = journal.debitEntity;

    debitContainer.innerHTML = '';
    creditContainer.innerHTML = '';

    const createEntryElement = (entry, side) => {
        const borderColor = side === 'debit' ? 'border-green-100' : 'border-red-100';
        const dateColor = side === 'debit' ? 'text-green-600' : 'text-red-600';

        return `
        <div class="border-b-4 border-r-4 ${borderColor} p-2 text-sm">
            <div class="flex flex-col justify-between border-b-2 border-gray-200 pb-2">
                <span class="font-bold ${dateColor}">${entry.date}</span>
            </div>
            <div class="flex flex-col justify-center items-center border-b-2 border-gray-200 p-2">
                <span class="text-xs text-gray-500">${entry.code}</span>
                <span class="font-bold text-gray-700 text-center">${entry.name}</span>
            </div>
            <div>
                <p class="font-bold text-gray-900 text-right mt-2">$${(entry.amount || 0).toLocaleString()}</p>
            </div>
        </div>
    `;
    };

    journal.debit.forEach(entry => {
        debitContainer.innerHTML += createEntryElement(entry, 'debit');
    });
    journal.credit.forEach(entry => {
        creditContainer.innerHTML += createEntryElement(entry, 'credit');
    });

    // 檢查借貸平衡
    const totalDebit = journal.debit.reduce((sum, item) => sum + item.amount, 0);
    const totalCredit = journal.credit.reduce((sum, item) => sum + item.amount, 0);

    if (Math.abs(totalDebit - totalCredit) > 0.001) { // 處理浮點數誤差
        debitContainer.innerHTML += `<div class="text-xs text-red-500 font-bold p-2 bg-red-50 rounded text-center">注意：借貸不平衡！</div>`
    }

};


/**
 * @description 刪除舊的會計分錄
 * @param {string} oldTransactionID 交易紀錄表ID
 * @returns {Promise<void> boolean} 刪除是否成功 
 */
async function deleteOldJournal(oldTransactionID){
    const deleteResult = await deleteJournalEntriesToDB(oldTransactionID);

    if(deleteResult){
        console.log('舊的會計分錄已刪除');
        return true;
    }else{
        console.log('舊的會計分錄刪除失敗');
        return false;
    }
    
}


/**
 * @description 交易表單資料 轉換成 會計分錄需求格式
 * @param {object} formData 交易表單資料
 * @returns {Promise<void> object} 會計分錄資料 
 */
async function transformFormDataToJournalData(feeCheck,formData){    

    const accountInfo = await getAccountTypeById(formData.accountId);
    const entityInfo = await getEntityNameById(formData.entityId, formData.entityType);
    let mainAccountName = accountInfo.name;
    let mainAccountType = accountInfo.type;
    let counterpartyName = entityInfo.name;

    // 生成會計分錄
    const journalFormData = {
        type: formData.transactionType,
        status: formData.paymentStatus,
        mainAccountName: mainAccountName,
        mainAccountType: mainAccountType,
        counterpartyName: counterpartyName || '未指定',
        mainAmount:  parseFloat(formData.amount),//輸入為含稅金額
        taxAmount:  parseFloat(formData.taxAmount) || 0,
        feeAmount: feeCheck ? parseFloat(formData.fee) || 0 : 0,
        totalAmount: formData.amount - (formData.taxAmount || 0),//未稅金額
        paymentDate: formData.paymentDate || formData.invoiceDate,
        invoiceDate: formData.invoiceDate || formData.paymentDate,
        description: await getPaymentDescriptionName(formData.paymentDescription) || '未選擇',
        descriptionCode: Number(formData.paymentDescription) || 'XXXX'
    };
    
    return journalFormData;

}

/**
 * @description 儲存新的會計分錄
 * @param {string} transactionID 交易紀錄表ID
 * @param {object} formData 交易表單資料
 * @returns {Promise<void> boolean} 儲存是否成功 
 */
async function saveNewJournal(transactionID, formData){
    try {
        // 生成會計分錄
        const { feeToggle } = getTransactionFormCard_DOMElements();
        const journalFormData = await transformFormDataToJournalData(feeToggle.checked,formData);

        const mjournal = new Journal(journalFormData);
        const journalList = mjournal.getJournal();
        console.log('會計分錄成功',journalList);
        // 儲存會計分錄到資料庫
        const saveResult =  await saveJournalEntriesToDB(transactionID, journalList);
        if(saveResult){
            console.log('新的會計分錄已儲存');
            return true;
        }else{
            console.log('新的會計分錄儲存失敗');
            return false;
        }
    } catch (journalError) {
        console.error('儲存會計分錄失敗：', journalError);
        // 會計分錄儲存失敗不影響交易儲存，但要記錄錯誤
    }
}


document.addEventListener('DOMContentLoaded', () => {
    //getTransactionFormCard_DOMElements is not defined 
    if(typeof getTransactionFormCard_DOMElements !== 'function'){
        console.log('getTransactionFormCard_DOMElements is not defined，目前未使用。');
    }else{
        const {form, paymentDescriptionCodeInput}= getTransactionFormCard_DOMElements();

        // --- 事件監聽 ---
        form.addEventListener('input', updatePreview);
        form.addEventListener('change', updatePreview);

        paymentDescriptionCodeInput.addEventListener('change', updatePreview);
    }
});