<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改善版批次新增交易 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 樣式庫載入 -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Firebase 相關庫載入 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script>

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <script src="../../common/utils/DatabaseErrors.js"></script>


    <!-- 自定義樣式 -->
    <style>
        /* 導航選單懸停效果 */
        .nav-item:hover>.submenu {
            display: block; /* 懸停時顯示子選單 */
        }

        /* 子選單基本樣式 */
        .submenu {
            display: none; /* 預設隱藏 */
            position: absolute; /* 絕對定位 */
            background-color: white; /* 白色背景 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 陰影效果 */
            z-index: 1000; /* 層級設定 */
            top: 100%; /* 位於父元素下方 */
            left: 0; /* 左對齊 */
        }

        /* 多層子選單定位 */
        .submenu .submenu {
            top: 0; /* 與父選單同高 */
            left: 100%; /* 位於父選單右側 */
        }

        /* 導航項目相對定位 */
        .nav-item {
            position: relative; /* 相對定位以支援子選單 */
        }
    </style>
    <!-- 導航功能載入 -->
    <script src="../../common/navigation/navigation.js"></script>
    <style>
        .batch-table-container {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .validation-error {
            border-color: #ef4444 !important;
            background-color: #fef2f2 !important;
        }
        
        .success-row {
            background-color: #f0fdf4 !important;
        }
        
        .error-row {
            background-color: #fef2f2 !important;
        }
        
        .progress-modal {
            backdrop-filter: blur(4px);
        }
    </style>


</head>

<body class="bg-gray-100">
    <!-- 頁面頂部導航 -->
    <div id="navbar-container"></div>

    <main class="container mx-auto px-4 py-8">
        <!-- 頁面標題 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">改善版批次新增交易</h1>
            <p class="text-gray-600">一次性填寫多筆交易資訊，支援範本和匯入功能</p>
        </div>

        <!-- 快速操作區 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">快速操作</h2>
                <div class="flex gap-2">
                    <button id="loadTemplateBtn" class="px-3 py-1 bg-purple-100 rounded text-sm hover:bg-purple-200">
                        <i class="fas fa-file-import mr-1"></i>載入範本
                    </button>
                    <button id="saveTemplateBtn" class="px-3 py-1 bg-green-100 rounded text-sm hover:bg-green-200">
                        <i class="fas fa-save mr-1"></i>儲存範本
                    </button>
                    <button id="importCsvBtn" class="px-3 py-1 bg-blue-100 rounded text-sm hover:bg-blue-200">
                        <i class="fas fa-upload mr-1"></i>匯入CSV
                    </button>
                    <button id="exportCsvBtn" class="px-3 py-1 bg-yellow-100 rounded text-sm hover:bg-yellow-200">
                        <i class="fas fa-download mr-1"></i>匯出CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- 共同屬性區域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">共同屬性設定</h2>
                <div class="flex gap-2">
                    <button id="minimalPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm hover:bg-blue-200">最小共同屬性</button>
                    <button id="standardPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm hover:bg-blue-200">標準模式</button>
                    <button id="maximalPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm hover:bg-blue-200">最大共同屬性</button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 共同屬性選擇區 -->
                <div class="col-span-1 border-r pr-4">
                    <h3 class="font-bold mb-2 text-sm text-gray-700">選擇共同屬性</h3>
                    <div class="space-y-2" id="commonPropertiesSelector">
                        <!-- 動態生成的共同屬性選擇器 -->
                    </div>
                </div>

                <!-- 共同屬性表單區 -->
                <div class="col-span-2" id="commonPropertiesForm">
                    <!-- 動態生成的共同屬性表單 -->
                </div>
            </div>
        </div>

        <!-- 批次交易表格 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">批次交易明細</h2>
                <div class="flex gap-2">
                    <button id="addRowBtn" class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600">
                        <i class="fas fa-plus mr-1"></i>新增一行
                    </button>
                    <button id="copyLastRowBtn" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
                        <i class="fas fa-copy mr-1"></i>複製上一行
                    </button>
                    <button id="clearAllBtn" class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600">
                        <i class="fas fa-trash mr-1"></i>清空全部
                    </button>
                </div>
            </div>

            <!-- 統計資訊 -->
            <div class="mb-4 p-3 bg-gray-50 rounded">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600">總筆數：</span>
                        <span id="totalRows" class="font-bold">0</span>
                    </div>
                    <div>
                        <span class="text-gray-600">總金額：</span>
                        <span id="totalAmount" class="font-bold">$0</span>
                    </div>
                    <div>
                        <span class="text-gray-600">總稅額：</span>
                        <span id="totalTax" class="font-bold">$0</span>
                    </div>
                    <div>
                        <span class="text-gray-600">有效筆數：</span>
                        <span id="validRows" class="font-bold text-green-600">0</span>
                    </div>
                </div>
            </div>

            <div class="batch-table-container">
                <table class="w-full border-collapse" id="batchTable">
                    <thead class="sticky top-0 bg-gray-100">
                        <tr>
                            <th class="p-2 border">
                                <input type="checkbox" id="selectAllRows" title="全選/取消全選">
                            </th>
                            <th class="p-2 border">序號</th>
                            <th class="p-2 border">金額 <span class="text-red-500">*</span></th>
                            <!-- 其他欄位將根據共同屬性動態生成 -->
                            <th class="p-2 border">備註</th>
                            <th class="p-2 border">狀態</th>
                            <th class="p-2 border">操作</th>
                        </tr>
                    </thead>
                    <tbody id="batchTableBody">
                        <!-- 動態生成的表格行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 批次操作按鈕 -->
        <div class="flex justify-between items-center">
            <div class="flex gap-2">
                <button id="validateAllBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-check-circle mr-1"></i>驗證全部
                </button>
                <button id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    取消
                </button>
            </div>
            <div class="flex gap-2">
                <button id="previewBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-eye mr-1"></i>預覽
                </button>
                <button id="saveSelectedBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-check mr-1"></i>儲存選中
                </button>
                <button id="saveAllBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-1"></i>批次儲存
                </button>
            </div>
        </div>

        <!-- 預覽模態框 -->
        <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white p-6 rounded-lg w-5/6 max-h-5/6 overflow-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">交易預覽</h3>
                    <button id="closePreviewBtn" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="previewContent" class="mb-4">
                    <!-- 預覽內容將在JS中生成 -->
                </div>
            </div>
        </div>

        <!-- 範本管理模態框 -->
        <div id="templateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white p-6 rounded-lg w-96">
                <h3 class="text-xl font-bold mb-4">範本管理</h3>
                <div id="templateContent">
                    <!-- 範本內容 -->
                </div>
                <div class="flex justify-end gap-2 mt-4">
                    <button id="closeTemplateBtn" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">取消</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 頁面底部 -->
    <div id="footer-container"></div>

    <!-- 載入現有功能模組 -->
    <script src="transactionCreate_EntitySearch_Module.js"></script>
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    <script src="transactionCreate_PaymentDescriptionMegaMenu_Module.js"></script>

    <!-- 改善版批次交易模組 -->
    <script src="transactionBatchCreate_Enhanced_Module.js"></script>
    

</body>
</html>
