/**
 * @file transactionBatchCreate_Enhanced_Module.js
 * @description 改善版批次交易輸入模組
 * - 增強錯誤處理和使用者體驗
 * - 優化效能和資料處理
 * - 改善模組架構和依賴管理
 */

// 批次交易服務類
class TransactionBatchService {
    constructor() {
        this.dependencies = {
            entitySearchManager: null,
            paymentDescriptionRenderer: null,
            accountsData: [],
            taxTypesData: []
        };
        this.validators = new BatchValidators();
        this.errorHandler = new BatchErrorHandler();
        this.progressTracker = new ProgressTracker();
    }

    /**
     * 初始化服務
     */
    async initialize() {
        try {
            await this.checkDependencies();
            await this.loadRequiredData();
            this.setupEventListeners();
            return true;
        } catch (error) {
            this.errorHandler.showError('初始化失敗', error.message);
            return false;
        }
    }

    /**
     * 檢查依賴模組
     */
    async checkDependencies() {
        const requiredFunctions = [
            'getAccountsAll',
            'getTaxTypesRatesAll',
            'getEntitiesAll',
            'getEmployeesAll',
            'saveTransactionToDB',
            'saveNewJournal'
        ];

        const missingFunctions = requiredFunctions.filter(fn => typeof window[fn] !== 'function');
        
        if (missingFunctions.length > 0) {
            throw new Error(`缺少必要函數: ${missingFunctions.join(', ')}`);
        }

        // 檢查類別
        if (typeof EntitySearchManager === 'undefined') {
            console.warn('EntitySearchManager 未定義，將使用簡化版本');
        }
    }

    /**
     * 載入必要資料
     */
    async loadRequiredData() {
        try {
            const [accounts, taxTypes, entities, employees] = await Promise.all([
                getAccountsAll(),
                getTaxTypesRatesAll(),
                getEntitiesAll(),
                getEmployeesAll()
            ]);

            this.dependencies.accountsData = accounts;
            this.dependencies.taxTypesData = taxTypes;

            // 初始化交易對象管理器
            if (typeof EntitySearchManager !== 'undefined') {
                this.dependencies.entitySearchManager = new EntitySearchManager(accounts, employees, entities);
            }

        } catch (error) {
            throw new Error(`載入資料失敗: ${error.message}`);
        }
    }

    /**
     * 驗證批次交易資料
     */
    validateBatchData(transactions) {
        const errors = [];
        
        transactions.forEach((tx, index) => {
            const rowErrors = this.validators.validateTransaction(tx);
            if (rowErrors.length > 0) {
                errors.push({
                    row: index + 1,
                    errors: rowErrors
                });
            }
        });

        return errors;
    }

    /**
     * 批次保存交易（支援事務處理）
     */
    async saveBatchTransactions(transactions) {
        // 驗證資料
        const validationErrors = this.validateBatchData(transactions);
        if (validationErrors.length > 0) {
            this.errorHandler.showValidationErrors(validationErrors);
            return { success: false, errors: validationErrors };
        }

        // 顯示進度
        this.progressTracker.start(transactions.length);

        const results = [];
        let successCount = 0;

        try {
            for (let i = 0; i < transactions.length; i++) {
                this.progressTracker.update(i + 1, `處理第 ${i + 1} 筆交易...`);

                try {
                    const savedId = await this.saveTransaction(transactions[i]);
                    if (savedId) {
                        results.push({ success: true, index: i, id: savedId });
                        successCount++;
                    } else {
                        results.push({ success: false, index: i, error: '保存失敗' });
                    }
                } catch (error) {
                    results.push({ success: false, index: i, error: error.message });
                }

                // 短暫延遲避免界面凍結
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            this.progressTracker.complete();
            
            return {
                success: successCount === transactions.length,
                successCount,
                totalCount: transactions.length,
                results
            };

        } catch (error) {
            this.progressTracker.error();
            this.errorHandler.showError('批次保存失敗', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 保存單筆交易
     */
    async saveTransaction(transactionData) {
        // 計算稅額
        if (transactionData.taxTypeId) {
            const taxType = this.dependencies.taxTypesData.find(t => t.id === transactionData.taxTypeId);
            if (taxType && taxType.rate) {
                transactionData.taxAmount = TaxCalculator.calculate(transactionData.amount, taxType);
            }
        }

        // 保存交易
        const savedId = await saveTransactionToDB(transactionData);

        // 保存會計分錄
        if (savedId) {
            try {
                await saveNewJournal(savedId, transactionData);
            } catch (journalError) {
                console.error('儲存會計分錄失敗：', journalError);
                // 會計分錄失敗不影響交易保存
            }
        }

        return savedId;
    }
}

// 驗證器類
class BatchValidators {
    validateTransaction(transaction) {
        const errors = [];

        // 必填欄位驗證
        if (!transaction.amount || parseFloat(transaction.amount) <= 0) {
            errors.push('金額必須大於0');
        }

        if (!transaction.accountId) {
            errors.push('請選擇帳戶');
        }

        if (!transaction.paymentDate) {
            errors.push('請選擇收款/付款日期');
        }

        // 日期格式驗證
        if (transaction.paymentDate && !this.isValidDate(transaction.paymentDate)) {
            errors.push('收款/付款日期格式不正確');
        }

        // 金額範圍驗證
        const amount = parseFloat(transaction.amount);
        if (amount > ********) {
            errors.push('金額不能超過一千萬');
        }

        return errors;
    }

    isValidDate(dateString) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date);
    }
}

// 錯誤處理器類
class BatchErrorHandler {
    showError(title, message) {
        console.error(`${title}: ${message}`);
        alert(`${title}\n${message}`);
    }

    showValidationErrors(errors) {
        let message = '發現以下驗證錯誤：\n\n';
        errors.forEach(error => {
            message += `第 ${error.row} 行：\n`;
            error.errors.forEach(err => {
                message += `  - ${err}\n`;
            });
            message += '\n';
        });
        alert(message);
    }

    showSuccess(message) {
        alert(message);
    }
}

// 進度追蹤器類
class ProgressTracker {
    constructor() {
        this.modal = null;
    }

    start(total) {
        this.total = total;
        this.createProgressModal();
    }

    update(current, message) {
        if (this.modal) {
            const progressText = this.modal.querySelector('#progressText');
            const progressBar = this.modal.querySelector('#progressBar');
            
            if (progressText) progressText.textContent = message;
            if (progressBar) progressBar.style.width = `${(current / this.total) * 100}%`;
        }
    }

    complete() {
        if (this.modal) {
            document.body.removeChild(this.modal);
            this.modal = null;
        }
    }

    error() {
        this.complete();
    }

    createProgressModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        this.modal.innerHTML = `
            <div class="bg-white p-6 rounded-lg w-96">
                <h3 class="text-lg font-bold mb-4">正在處理批次交易</h3>
                <div class="mb-4">
                    <div id="progressText">準備中...</div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                </div>
            </div>
        `;
        document.body.appendChild(this.modal);
    }
}

// 稅額計算器類
class TaxCalculator {
    static calculate(amount, taxType) {
        if (!amount || !taxType || !taxType.rate) return 0;
        
        switch (taxType.calculationType || 'percentage') {
            case 'percentage':
                return Math.round(amount * (taxType.rate / 100));
            case 'fixed':
                return taxType.fixedAmount || 0;
            default:
                return 0;
        }
    }
}

// 全域實例
let batchService = null;

// 初始化函數
async function initializeBatchService() {
    batchService = new TransactionBatchService();
    const success = await batchService.initialize();
    
    if (!success) {
        console.error('批次服務初始化失敗');
        return false;
    }
    
    console.log('批次服務初始化成功');
    return true;
}

// 匯出給其他模組使用
window.TransactionBatchService = TransactionBatchService;
window.initializeBatchService = initializeBatchService;
